import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';

// Interfaces for the API response
interface Competitor {
  name: string;
  address: string;
  description: string;
}

interface TargetEntity {
  name: string;
  present: boolean;
  details: {
    address: string;
    description: string;
  };
}

export interface GoogleAiTrackingData {
  keyword: string;
  location: string;
  target_entity: TargetEntity;
  top_competitors: Competitor[];
}

@Injectable({
  providedIn: 'root',
})
export class GoogleAiTrackingService {
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<string | null>(null);

  loading$ = this.loadingSubject.asObservable();
  error$ = this.errorSubject.asObservable();

  constructor(private http: HttpClient) {}

  getTrackingData(accountGroupId: string): Observable<GoogleAiTrackingData[]> {
    this.loadingSubject.next(true);
    this.errorSubject.next(null);

    // TODO: Replace with actual API endpoint when available
    // For now, returning mock data that matches the expected format
    return this.getMockData().pipe(
      tap(() => this.loadingSubject.next(false)),
      catchError((error) => {
        this.loadingSubject.next(false);
        this.errorSubject.next(error.message || 'An error occurred while fetching tracking data');
        return throwError(() => error);
      }),
    );
  }

  private getMockData(): Observable<GoogleAiTrackingData[]> {
    // Mock data that matches the exact format specified in the requirements
    const mockData: GoogleAiTrackingData[] = [
      {
        keyword: "1. Saskatoon fast food\n2. Five Guys Saskatoon\n3. Fast food delivery Saskatchewan",
        location: "Saskatoon, SK, CA",
        target_entity: {
          details: {
            address: "1840 8 St E, Saskatoon, SK",
            description: "Known for made-to-order burgers, hot dogs, fries cooked in peanut oil, and free peanuts while you wait."
          },
          name: "Five Guys 1E",
          present: true
        },
        top_competitors: [
          {
            address: "Address not provided",
            description: "Serves burgers, Japa fries, and chicken.",
            name: "JAPA SHACK"
          }
        ]
      },
      {
        keyword: "1. Saskatoon pizza delivery\n2. Best pizza Saskatoon\n3. Pizza near me",
        location: "Saskatoon, SK, CA",
        target_entity: {
          details: {
            address: "123 Main St, Saskatoon, SK",
            description: "Family-owned pizzeria serving authentic Italian pizza with fresh ingredients."
          },
          name: "Mama's Pizza",
          present: false
        },
        top_competitors: [
          {
            address: "456 Broadway Ave, Saskatoon, SK",
            description: "Artisan pizza with wood-fired oven and premium toppings.",
            name: "Pizza Palace"
          },
          {
            address: "789 8th St E, Saskatoon, SK",
            description: "Quick delivery pizza with generous portions and competitive prices.",
            name: "Express Pizza"
          }
        ]
      }
    ];

    return of(mockData);
  }

  // TODO: Implement actual API call when endpoint is available
  private callApi(accountGroupId: string): Observable<GoogleAiTrackingData[]> {
    // This would be the actual API call
    // return this.http.get<GoogleAiTrackingData[]>(`/api/google-ai-tracking/${accountGroupId}`);
    return this.getMockData();
  }
} 