.google-ai-tracking-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  p {
    margin-top: 16px;
    color: #666;
    font-size: 16px;
  }
}

// Error State
.error-container {
  display: flex;
  justify-content: center;
  padding: 40px 20px;
}

.error-card {
  max-width: 500px;
  text-align: center;

  .error-icon {
    font-size: 48px;
    color: #f44336;
    margin-bottom: 16px;
  }

  h3 {
    margin: 16px 0;
    color: #333;
  }

  p {
    color: #666;
    line-height: 1.5;
  }
}

// No Data State
.no-data-container {
  display: flex;
  justify-content: center;
  padding: 40px 20px;
}

.no-data-card {
  max-width: 500px;
  text-align: center;

  .no-data-icon {
    font-size: 48px;
    color: #999;
    margin-bottom: 16px;
  }

  h3 {
    margin: 16px 0;
    color: #333;
  }

  p {
    color: #666;
    line-height: 1.5;
  }
}

// Tracking Cards
.tracking-cards {
  display: grid;
  gap: 24px;
  grid-template-columns: 1fr;

  @media (min-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  }
}

.tracking-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .card-title {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;

    .location-icon {
      margin-right: 8px;
      color: #1976d2;
    }
  }
}

// Section Styling
.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 24px 0 16px 0;

  mat-icon {
    margin-right: 8px;
    color: #666;
  }
}

// Keywords Section
.keywords-section {
  margin-bottom: 20px;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-chip {
  background-color: #e3f2fd;
  color: #1976d2;
  font-size: 14px;
  border-radius: 16px;
  padding: 4px 12px;
  margin: 0;
}

// Business Visibility Section
.visibility-section {
  margin: 20px 0;
}

.business-status {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-top: 12px;
}

.status-indicator {
  flex-shrink: 0;
}

.business-info {
  flex: 1;
}

.business-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.business-details {
  p {
    display: flex;
    align-items: flex-start;
    margin: 8px 0;
    color: #666;
    line-height: 1.5;

    .detail-icon {
      margin-right: 8px;
      margin-top: 2px;
      color: #999;
      font-size: 18px;
    }
  }
}

// Competitors Section
.competitors-section {
  margin-top: 20px;
}

.competitors-list {
  margin-top: 12px;
}

.competitor-panel {
  margin-bottom: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  ::ng-deep .mat-expansion-panel-header {
    padding: 16px;
    border-radius: 8px;
  }

  ::ng-deep .mat-expansion-panel-body {
    padding: 16px;
  }
}

.competitor-icon {
  margin-right: 8px;
  color: #666;
}

.competitor-details {
  p {
    display: flex;
    align-items: flex-start;
    margin: 8px 0;
    color: #666;
    line-height: 1.5;

    .detail-icon {
      margin-right: 8px;
      margin-top: 2px;
      color: #999;
      font-size: 18px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .google-ai-tracking-container {
    padding: 16px;
  }

  .tracking-cards {
    grid-template-columns: 1fr;
  }

  .business-status {
    flex-direction: column;
    gap: 12px;
  }

  .keywords-list {
    gap: 6px;
  }

  .keyword-chip {
    font-size: 12px;
    padding: 2px 8px;
  }
} 