# Google AI Tracking Feature

## Overview

The Google AI Tracking feature helps businesses understand their visibility in Google's AI-powered search results. This page displays tracking data that shows whether a business appears in Google AI search results and provides insights into competitor performance when the business is not visible.

## Features

### 1. Navigation Integration
- Added to the left sidebar navigation with the label "Google AI Tracking"
- Uses the `psychology` icon to represent AI/ML functionality
- Accessible via `/edit/account/:accountGroupId/app/google-ai-tracking`

### 2. Data Display
- **Business Visibility Status**: Clear indication of whether the business is visible in Google AI search results
- **Keywords Section**: Displays tracked keywords in an easy-to-read chip format
- **Location Information**: Shows the location being monitored
- **Business Details**: When visible, displays business name, address, and description
- **Competitors Section**: Shows top competitors with expandable details when business is not visible

### 3. User Experience
- **Loading States**: Shows loading spinner while fetching data
- **Error Handling**: Displays user-friendly error messages
- **Empty States**: Informative message when no tracking data is available
- **Responsive Design**: Works well on desktop and mobile devices

## API Integration

### Expected API Response Format
The feature expects data in the following JSON format:

```json
[
  {
    "keyword": "1. Saskatoon fast food\n2. Five Guys Saskatoon\n3. Fast food delivery Saskatchewan",
    "location": "Saskatoon, SK, CA",
    "target_entity": {
      "details": {
        "address": "1840 8 St E, Saskatoon, SK",
        "description": "Known for made-to-order burgers, hot dogs, fries cooked in peanut oil, and free peanuts while you wait."
      },
      "name": "Five Guys 1E",
      "present": true
    },
    "top_competitors": [
      {
        "address": "Address not provided",
        "description": "Serves burgers, Japa fries, and chicken.",
        "name": "JAPA SHACK"
      }
    ]
  }
]
```

### Service Implementation
- `GoogleAiTrackingService` handles API calls and data management
- Currently uses mock data that matches the expected API format
- Ready for integration with actual backend API endpoint

## File Structure

```
google-ai-tracking/
├── google-ai-tracking.component.ts      # Main component logic
├── google-ai-tracking.component.html    # Component template
├── google-ai-tracking.component.scss    # Component styles
├── google-ai-tracking.service.ts        # API service
├── google-ai-tracking.module.ts         # Module configuration
├── google-ai-tracking.routing.ts        # Routing configuration
└── README.md                           # This documentation
```

## Integration Points

### Navigation
- Added to `app.component.ts` in the `buildNavItems` method
- Uses translation key `NAVBAR.GOOGLE_AI_TRACKING`

### Routing
- Added routes in `app-routing.module.ts` for both `/edit/account/:accountGroupId` and `/edit/account/:accountGroupId/app` paths
- Lazy-loaded module for optimal performance

### Translations
- Added translation keys in `assets/i18n/en_devel.json`
- All user-facing text is internationalized

## Styling

The component uses:
- Material Design components for consistency
- Galaxy design system components (badges, loading spinner)
- Responsive grid layout
- Modern card-based design with hover effects
- Color-coded status indicators (green for visible, red for not visible)

## Analytics

The component tracks page views using the ProductAnalyticsService:
- Event: `google-ai-tracking-page`
- Project: `listing-builder-client`
- Action: `onload`

## Future Enhancements

1. **Real API Integration**: Replace mock data with actual backend API calls
2. **Data Refresh**: Add refresh functionality to update tracking data
3. **Historical Data**: Show tracking data over time with charts
4. **Keyword Management**: Allow users to add/remove tracked keywords
5. **Competitor Analysis**: More detailed competitor insights and comparisons
6. **Notifications**: Alert users when visibility status changes

## Testing

The component includes:
- Loading state handling
- Error state handling
- Empty state handling
- Responsive design testing
- Accessibility considerations

## Dependencies

- Angular Material (cards, icons, expansion panels, chips)
- Galaxy design system (badges, loading spinner)
- RxJS for reactive programming
- NgRx Translate for internationalization 