<app-page [pageTitle]="pageTitleKey$ | async | translate">
  <div class="google-ai-tracking-container">
    <!-- Loading State -->
    <div *ngIf="loading$ | async" class="loading-container">
      <glxy-loading-spinner></glxy-loading-spinner>
      <p>{{ 'GOOGLE_AI_TRACKING.LOADING' | translate }}</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error$ | async as error" class="error-container">
      <mat-card class="error-card">
        <mat-card-content>
          <mat-icon class="error-icon">error</mat-icon>
          <h3>{{ 'GOOGLE_AI_TRACKING.ERROR_TITLE' | translate }}</h3>
          <p>{{ error }}</p>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Data Display -->
    <div *ngIf="(loading$ | async) === false && (error$ | async) === null">
      <ng-container *ngIf="trackingData$ | async as trackingData">
        <!-- No Data State -->
        <div *ngIf="trackingData.length === 0" class="no-data-container">
          <mat-card class="no-data-card">
            <mat-card-content>
              <mat-icon class="no-data-icon">search_off</mat-icon>
              <h3>{{ 'GOOGLE_AI_TRACKING.NO_DATA_TITLE' | translate }}</h3>
              <p>{{ 'GOOGLE_AI_TRACKING.NO_DATA_DESCRIPTION' | translate }}</p>
            </mat-card-content>
          </mat-card>
        </div>

        <!-- Data Cards -->
        <div *ngIf="trackingData.length > 0" class="tracking-cards">
          <mat-card *ngFor="let item of trackingData; trackBy: trackByIndex" class="tracking-card">
            <mat-card-header>
              <mat-card-title class="card-title">
                <mat-icon class="location-icon">location_on</mat-icon>
                {{ item.location }}
              </mat-card-title>
            </mat-card-header>

            <mat-card-content>
              <!-- Keywords Section -->
              <div class="keywords-section">
                <h4 class="section-title">
                  <mat-icon>search</mat-icon>
                  {{ 'GOOGLE_AI_TRACKING.KEYWORDS' | translate }}
                </h4>
                <div class="keywords-list">
                  <mat-chip *ngFor="let keyword of getKeywordsList(item.keyword)" class="keyword-chip">
                    {{ keyword }}
                  </mat-chip>
                </div>
              </div>

              <mat-divider></mat-divider>

              <!-- Business Visibility Status -->
              <div class="visibility-section">
                <h4 class="section-title">
                  <mat-icon>visibility</mat-icon>
                  {{ 'GOOGLE_AI_TRACKING.BUSINESS_VISIBILITY' | translate }}
                </h4>
                
                <div class="business-status">
                  <div class="status-indicator">
                    <glxy-badge 
                      [color]="getVisibilityStatus(item.target_entity.present).color === 'green' ? 'green' : 'red'"
                      [size]="'normal'"
                    >
                      {{ getVisibilityStatus(item.target_entity.present).text | translate }}
                    </glxy-badge>
                  </div>
                  
                  <div class="business-info">
                    <h5 class="business-name">{{ item.target_entity.name }}</h5>
                    
                    <!-- Show business details when present -->
                    <div *ngIf="item.target_entity.present" class="business-details">
                      <p class="business-address">
                        <mat-icon class="detail-icon">place</mat-icon>
                        {{ item.target_entity.details.address }}
                      </p>
                      <p class="business-description">
                        <mat-icon class="detail-icon">description</mat-icon>
                        {{ item.target_entity.details.description }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Competitors Section -->
              <div *ngIf="item.top_competitors.length > 0" class="competitors-section">
                <mat-divider></mat-divider>
                
                <h4 class="section-title">
                  <mat-icon>business</mat-icon>
                  {{ 'GOOGLE_AI_TRACKING.TOP_COMPETITORS' | translate }}
                </h4>
                
                <div class="competitors-list">
                  <mat-expansion-panel *ngFor="let competitor of item.top_competitors; trackBy: trackByIndex" class="competitor-panel">
                    <mat-expansion-panel-header>
                      <mat-panel-title>
                        <mat-icon class="competitor-icon">storefront</mat-icon>
                        {{ competitor.name }}
                      </mat-panel-title>
                    </mat-expansion-panel-header>
                    
                    <div class="competitor-details">
                      <p *ngIf="competitor.address !== 'Address not provided'" class="competitor-address">
                        <mat-icon class="detail-icon">place</mat-icon>
                        {{ competitor.address }}
                      </p>
                      <p class="competitor-description">
                        <mat-icon class="detail-icon">description</mat-icon>
                        {{ competitor.description }}
                      </p>
                    </div>
                  </mat-expansion-panel>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </ng-container>
    </div>
  </div>
</app-page> 