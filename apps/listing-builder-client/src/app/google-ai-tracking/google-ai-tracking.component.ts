import { Component, Inject, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Observable, of, Subject } from 'rxjs';
import { shareReplay, switchMap } from 'rxjs/operators';
import { AGIDTOKEN } from '../app.module';
import { GoogleAiTrackingService } from './google-ai-tracking.service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

// Interfaces for the API response
interface Competitor {
  name: string;
  address: string;
  description: string;
}

interface TargetEntity {
  name: string;
  present: boolean;
  details: {
    address: string;
    description: string;
  };
}

interface GoogleAiTrackingData {
  keyword: string;
  location: string;
  target_entity: TargetEntity;
  top_competitors: Competitor[];
}

@Component({
  selector: 'app-google-ai-tracking',
  templateUrl: './google-ai-tracking.component.html',
  styleUrls: ['./google-ai-tracking.component.scss'],
  standalone: false,
})
export class GoogleAiTrackingComponent implements On<PERSON>nit, OnDestroy {
  pageTitleKey$ = of('GOOGLE_AI_TRACKING.TITLE');
  trackingData$: Observable<GoogleAiTrackingData[]>;
  loading$: Observable<boolean>;
  error$: Observable<string | null>;
  
  private destroy$ = new Subject<void>();

  constructor(
    @Inject(AGIDTOKEN) private agid$: Observable<string>,
    private googleAiTrackingService: GoogleAiTrackingService,
    private productAnalyticsService: ProductAnalyticsService,
  ) {}

  ngOnInit(): void {
    this.trackingData$ = this.agid$.pipe(
      switchMap((accountGroupId) => this.googleAiTrackingService.getTrackingData(accountGroupId)),
      shareReplay(1),
    );

    this.loading$ = this.googleAiTrackingService.loading$;
    this.error$ = this.googleAiTrackingService.error$;

    // Track page view
    this.productAnalyticsService.trackEvent('google-ai-tracking-page', 'listing-builder-client', 'onload', 0);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Helper methods for template
  getKeywordsList(keywords: string): string[] {
    return keywords.split('\n').filter(keyword => keyword.trim().length > 0);
  }

  getVisibilityStatus(present: boolean): { text: string; color: string } {
    return present 
      ? { text: 'GOOGLE_AI_TRACKING.VISIBLE', color: 'green' }
      : { text: 'GOOGLE_AI_TRACKING.NOT_VISIBLE', color: 'red' };
  }

  trackByIndex(index: number): number {
    return index;
  }
} 