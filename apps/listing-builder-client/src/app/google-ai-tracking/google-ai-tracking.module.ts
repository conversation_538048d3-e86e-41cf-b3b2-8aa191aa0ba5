import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GoogleAiTrackingComponent } from './google-ai-tracking.component';
import { GoogleAiTrackingRouting } from './google-ai-tracking.routing';
import { SharedModule } from '../shared/shared.module';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';

@NgModule({
  declarations: [GoogleAiTrackingComponent],
  imports: [
    CommonModule,
    GoogleAiTrackingRouting,
    SharedModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatChipsModule,
    MatDividerModule,
    MatExpansionModule,
    GalaxyLoadingSpinnerModule,
    GalaxyBadgeModule,
  ],
  exports: [GoogleAiTrackingComponent],
})
export class GoogleAiTrackingModule {} 