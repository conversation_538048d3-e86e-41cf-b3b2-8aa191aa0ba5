import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';

import { devServer } from '../globals';
import { LocalLoginComponent } from './local-login/local-login.component';
import { MyListingComponent } from './my-listing/my-listing.component';
import { LocalNonAngularComponent } from './non-angular.component';
import { OverviewComponent } from './overview/overview.component';

// tslint:disable-line:max-line-length
const routes: Routes = [
  {
    path: 'edit/account/:accountGroupId',
    children: [
      {
        path: 'overview',
        component: OverviewComponent,
      },
      {
        path: 'analytics',
        loadChildren: () => import('./analytics/analytics.module').then((m) => m.AnalyticsModule),
      },
      {
        path: 'google-insights',
        redirectTo: 'analytics/google',
      },
      {
        path: 'bing-insights',
        redirectTo: 'analytics/bing',
      },
      {
        path: 'listings',
        redirectTo: 'citations',
      },
      {
        path: 'citations',
        loadChildren: () => import('./citations/citations.module').then((m) => m.CitationsModule),
      },
      {
        path: 'social-account-connect',
        loadChildren: () =>
          import('./social-account-connect/social-account-connect.module').then((m) => m.SocialAccountConnectModule),
      },
      {
        path: 'select-gmb-location',
        redirectTo: 'social-account-connect',
      },
      {
        path: 'add-social-service',
        redirectTo: 'social-account-connect',
      },
      {
        path: 'listing-sync',
        loadChildren: () =>
          import('./listings-management/listings-management.module').then((m) => m.ListingsManagementModule),
      },
      {
        path: 'local-seo',
        loadChildren: () => import('./keyword-tracking/keyword-tracking.module').then((m) => m.KeywordTrackingModule),
      },
      {
        path: 'google-ai-tracking',
        loadChildren: () => import('./google-ai-tracking/google-ai-tracking.module').then((m) => m.GoogleAiTrackingModule),
      },
    ],
  },
  {
    path: 'edit/account/:accountGroupId/app',
    children: [
      {
        path: 'overview',
        component: OverviewComponent,
      },
      {
        path: 'analytics',
        loadChildren: () => import('./analytics/analytics.module').then((m) => m.AnalyticsModule),
      },
      {
        path: 'google-insights',
        redirectTo: 'analytics/google',
      },
      {
        path: 'bing-insights',
        redirectTo: 'analytics/bing',
      },
      {
        path: 'listings',
        redirectTo: 'citations',
      },
      {
        path: 'citations',
        loadChildren: () => import('./citations/citations.module').then((m) => m.CitationsModule),
      },
      {
        path: 'social-account-connect',
        loadChildren: () =>
          import('./social-account-connect/social-account-connect.module').then((m) => m.SocialAccountConnectModule),
      },
      {
        path: 'select-gmb-location',
        redirectTo: 'social-account-connect',
      },
      {
        path: 'add-social-service',
        redirectTo: 'social-account-connect',
      },
      {
        path: 'listing-profile',
        loadChildren: () => import('./listing-profile/listing-profile.module').then((m) => m.ListingProfileModule),
      },
      {
        path: 'website',
        component: MyListingComponent,
      },
      {
        path: 'listing-sync',
        loadChildren: () =>
          import('./listings-management/listings-management.module').then((m) => m.ListingsManagementModule),
      },
      {
        path: 'local-seo',
        loadChildren: () => import('./keyword-tracking/keyword-tracking.module').then((m) => m.KeywordTrackingModule),
      },
      {
        path: 'google-ai-tracking',
        loadChildren: () => import('./google-ai-tracking/google-ai-tracking.module').then((m) => m.GoogleAiTrackingModule),
      },
    ],
  },
  {
    path: 'partner/:partnerId',
    loadChildren: () => import('./partner/partner.module').then((m) => m.PartnerModule),
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(
      devServer
        ? [
            ...routes,
            { path: 'login', component: LocalLoginComponent },
            { path: '**', component: LocalNonAngularComponent },
          ]
        : routes,
      {
        preloadingStrategy: PreloadAllModules,
      },
    ),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
